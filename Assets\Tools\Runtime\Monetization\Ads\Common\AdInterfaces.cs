using System;
using System.Threading;
using UnityEngine;
using UnityEngine.Events;

namespace SmartVertex.Tools
{
    public interface IAdUnit
    {
        AdType type { get; }
        string placementId { get; }
        AdState state { get; }

        Awaitable<bool> LoadAsync(TimeSpan timeout, CancellationToken ct);
        Awaitable<AdOperationResult> ShowAsync(CancellationToken ct);
        event UnityAction OnClicked;
        event UnityAction OnImpression;
        event UnityAction<string, double> OnPaid; // currencyCode, value
    }
}